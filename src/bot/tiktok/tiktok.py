import os
import random
from pathlib import Path
from typing import Optional, List, Dict, Any
from tiktok_uploader.upload import upload_video
from src.logger import get_logger
from src.music import MusicManager

LOGGER = get_logger(__file__)
COOKIE_FILE = Path(__file__).parent.parent.parent.parent / "assets" / "cookies.txt"
# Component-based description system for dynamic TikTok content
DESCRIPTION_COMPONENTS = {
    # Opening hooks - grab attention immediately
    "hooks": {
        "dividend_comparison": [
            "💰 Reinvesting dividends vs taking cash - which strategy wins?",
            "🔄 The dividend dilemma: compound or cash out?",
            "💸 Same stock, different dividend strategy - shocking results!",
            "🎯 Dividend reinvestment: worth it or waste of time?",
        ],
        "timing_strategies": [
            "📈📉 Buying at peaks vs lows - the ultimate timing test!",
            "🎢 Market timing experiment: worst vs best entry points!",
            "⚡ Peak buyer vs dip buyer - who comes out ahead?",
            "🔥 Perfect timing vs terrible timing - the results will shock you!",
        ],
        "company_showdown": [
            "🥊 Epic stock battle - which giant dominates?",
            "⚡ Tech titans clash - the ultimate investment showdown!",
            "🏆 Stock wars: only one can be the champion!",
            "🎯 Investment face-off - the results are incredible!",
        ],
        "crypto_battle": [
            "🚀 Crypto showdown - which rocket ship wins?",
            "💎 Digital gold rush - the ultimate crypto comparison!",
            "⚡ Moon mission battle - destination: financial freedom!",
            "🔥 Crypto wars - only diamond hands survive!",
        ],
        "single_strategy": [
            "💰 The investment journey that changed everything!",
            "🎯 One simple strategy, life-changing results!",
            "📈 The power of consistent investing revealed!",
            "⚡ Small investments, massive returns - here's how!",
        ]
    },

    # Middle content - build engagement and context
    "engagement": [
        "What would you have done?",
        "Which strategy would you choose?",
        "Drop your thoughts below! 👇",
        "Tag someone who needs to see this!",
        "Would you have the patience?",
        "This is why consistency matters!",
        "The power of compound growth! 📊",
        "Time in the market beats timing the market!",
    ],

    # Call-to-action phrases
    "cta": [
        "Follow for more investment insights! 🚀",
        "Like if this surprised you! 💯",
        "Share with your investing friends! 📤",
        "Comment your investment strategy! 💬",
        "Save this for later! 📌",
        "Which stock is next? 🤔",
    ],

    # Disclaimers - keep it short and punchy
    "disclaimers": [
        "⚠️ Not financial advice - just showing what could have happened!",
        "📊 Past performance ≠ future results. Always DYOR!",
        "💡 Educational content only - invest responsibly!",
        "🎯 Hypothetical scenario for learning purposes!",
    ]
}

# Fallback static description
DESCRIPTION = """
Stonks only go up, right? What do you think?

DISCLAIMER: This is a fictional scenario created for entertainment purposes only. Past performance is not indicative of future results. Always do your own research before making any investment decisions. Investment involves risk. Only invest with money you can afford to lose.

 #stocks #crypto #finance #investing #reelstonks #stockmarket #money #fyp #foryoupage #rich
"""


def generate_dynamic_description(
    strategies: List[Any] = None,
    investment_amount: int = 1000,
    years_ago: int = 10,
    interval: str = "Month",
    music_creator: str | bool = False,
    music_link: str | bool = False,
    hashtag_style: str = "traditional",
) -> str:
    """Generate dynamic TikTok description based on investment scenario.

    Creates engaging, varied descriptions using component-based system that adapts
    to different investment strategies and scenarios.

    Args:
        strategies: List of investment strategy objects
        investment_amount: Dollar amount invested per interval
        years_ago: Number of years back for analysis
        interval: Investment frequency
        music_creator: Music creator name for attribution
        music_link: Music link for attribution
        hashtag_style: Style of hashtags to use

    Returns:
        Dynamic description optimized for TikTok engagement
    """

    # Determine scenario type
    scenario_type = _detect_scenario_type(strategies) if strategies else "single_strategy"

    # Build description components
    hook = random.choice(DESCRIPTION_COMPONENTS["hooks"][scenario_type])
    engagement = random.choice(DESCRIPTION_COMPONENTS["engagement"])
    cta = random.choice(DESCRIPTION_COMPONENTS["cta"])
    disclaimer = random.choice(DESCRIPTION_COMPONENTS["disclaimers"])

    # Build base content
    base_content = f"{hook}\n\n{engagement}\n\n{cta}"

    # Add music section
    music_section = ""
    if music_creator:
        music_section += f"\n\n🎵 Music by {music_creator}"
    if music_link:
        music_section += f"\nCheck out: {music_link}"

    # Add disclaimer
    disclaimer_section = f"\n\n{disclaimer}"

    # Add hashtags
    hashtag_options = {
        "traditional": "\n\n #fyp #stocks #finance #investing #reelstonks #stockmarket #viral #money",
        "end_only": "\n\n\n#stocks #finance #investing #reelstonks",
        "mixed": "\n\nTags: #stocks #finance #investing #reelstonks",
        "separated": "\n\n--- \n#stocks #finance #investing #reelstonks \n---",
        "tiktok_style": "\n\n#fyp #stocks #finance #investing #viral #money",
    }
    hashtags = hashtag_options.get(hashtag_style, hashtag_options["traditional"])

    return base_content + music_section + disclaimer_section + hashtags


def _detect_scenario_type(strategies: List[Any]) -> str:
    """Detect the type of investment scenario based on strategies."""
    if not strategies:
        return "single_strategy"

    # Extract strategy information
    tickers = [getattr(s, 'ticker_symbol', '') for s in strategies]
    timings = [getattr(s, 'investment_timing', 'regular') for s in strategies]
    dividend_policies = [getattr(s, 'reinvest_dividends', False) for s in strategies]

    unique_companies = len(set(tickers))
    has_timing_strategies = any(t in ['peaks', 'lows'] for t in timings)
    has_dividend_comparison = len(set(dividend_policies)) > 1
    is_crypto = any('USD' in ticker for ticker in tickers)

    # Determine scenario type
    if unique_companies == 1 and has_dividend_comparison:
        return "dividend_comparison"
    elif has_timing_strategies:
        return "timing_strategies"
    elif is_crypto and unique_companies > 1:
        return "crypto_battle"
    elif unique_companies > 1:
        return "company_showdown"
    else:
        return "single_strategy"


def generate_description_single_line(
    music_creator: str | bool,
    music_link: str | bool = False,
    hashtag_style: str = "traditional",
) -> str:
    """
    Alternative description generator with proven TikTok API hashtag approaches.

    Args:
        music_creator: The name of the music creator.
        music_link: The link for the music.
        hashtag_style: Style of hashtags.

    Returns:
        Description with specified hashtag formatting optimized for TikTok API.
    """
    base_content = "Stonks only go up! What do you think?"
    disclaimer = "\n\nDISCLAIMER: This is a fictional scenario created for entertainment purposes only. Past performance is not indicative of future results. Always do your own research before making any investment decisions. Investment involves risk. Only invest with money you can afford to lose."

    music_section = ""
    if music_creator:
        music_section += f"\n\nMusic by {music_creator}"
    if music_link:
        music_section += f"\nCheck out: {music_link}"

    # Proven hashtag approaches for TikTok API
    hashtag_options = {
        # Most reliable: traditional single line
        "traditional": "\n\n #fyp #stocks #finance #investing #reelstonks #stockmarket #viral #money #fyp #foryoupage #rich ",
        # Only at the very end with extra spacing
        "end_only": "\n\n\n#stocks #finance #investing #reelstonks",
        # Mixed with text
        "mixed": "\n\nTags: #stocks #finance #investing #reelstonks",
        # With separators
        "separated": "\n\n--- \n#stocks #finance #investing #reelstonks \n---",
        # Popular TikTok format
        "tiktok_style": "\n\n#fyp #stocks #finance #investing #viral #money",
    }

    hashtags = hashtag_options.get(hashtag_style, "traditional")
    return base_content + music_section + disclaimer + hashtags


def filter_bmp_characters(text: str) -> str:
    """Filter out characters outside the Basic Multilingual Plane (BMP).

    ChromeDriver only supports characters in the BMP (Unicode code points 0-65535).
    This function removes any characters outside this range to prevent upload errors.

    Args:
        text (str): Input text that may contain non-BMP characters

    Returns:
        str: Text with non-BMP characters removed
    """
    return "".join(char for char in text if ord(char) <= 0xFFFF)


def titktok_upload_manager(
    video_path: str,
    music_filename: Optional[str] = None,
    strategies: Optional[List[Any]] = None,
    investment_amount: int = 1000,
    years_ago: int = 10,
    interval: str = "Month",
    cookies_file: str = COOKIE_FILE,
    headless: bool = True,
    privacy_type: str = "private",
) -> bool:
    """Uploads a video to TikTok using a cookies file for authentication.

    Creates a dynamic description based on investment strategies and music attribution,
    using a component-based system for varied, engaging content.

    Args:
        video_path (str): The path to the video file.
        music_filename (Optional[str]): The music filename to get attribution for.
            If None, uses the first available music from MusicManager.
        strategies (Optional[List[Any]]): List of investment strategy objects for dynamic descriptions.
        investment_amount (int): Dollar amount invested per interval.
        years_ago (int): Number of years back for analysis.
        interval (str): Investment frequency.
        cookies_file (str, optional): The path to the cookies file. Defaults to COOKIE_FILE.
        headless (bool, optional): Whether to run in headless mode. Defaults to True.
        privacy_type (str, optional): Privacy setting for the video. Defaults to "private".

    Returns:
        bool: True if the video was successfully uploaded, False otherwise.
    """

    LOGGER.activate()

    if not os.path.exists(cookies_file):
        LOGGER.error(
            f"❌ Cookie file not found! Please ensure '{cookies_file}' exists."
        )
        return False

    if not os.path.exists(video_path):
        LOGGER.error(f"❌ Video file not found! Please ensure '{video_path}' exists.")
        return False

    try:
        # Load music TOML data and generate description based on creator and link
        music_toml_data = None
        try:
            music_manager = MusicManager()
            if music_manager.has_music():
                selected_music = music_manager.select_music(music_filename)
                if selected_music:
                    # Load the full TOML config to get creator and link
                    import toml

                    config_file = music_manager.music_dir / "creators.toml"
                    if config_file.exists():
                        config_data = toml.load(config_file)
                        music_toml_data = config_data.get(selected_music.filename, {})
        except Exception as e:
            LOGGER.warning(f"Could not load music data: {e}")

        # Generate dynamic description based on strategies and music
        try:
            if strategies:
                # Use dynamic description generator with strategy information
                LOGGER.info("Generating dynamic description based on investment strategies")
                description = generate_dynamic_description(
                    strategies=strategies,
                    investment_amount=investment_amount,
                    years_ago=years_ago,
                    interval=interval,
                    music_creator=music_toml_data.get("creator", False) if music_toml_data else False,
                    music_link=music_toml_data.get("link", False) if music_toml_data else False,
                    hashtag_style="traditional"
                )
            elif music_toml_data and (music_toml_data.get("creator", False) or music_toml_data.get("link", False)):
                # Use single-line generator with music data
                LOGGER.info("Generating description from music TOML data")
                description = generate_description_single_line(
                    music_toml_data.get("creator", False),
                    music_toml_data.get("link", False),
                    hashtag_style="traditional"
                )
            else:
                # Fallback to static description
                LOGGER.info("Using fallback static description")
                description = DESCRIPTION
        except Exception as e:
            LOGGER.warning(f"Failed to generate dynamic description: {e}, using fallback")
            description = DESCRIPTION

        # Filter description to remove non-BMP characters
        filtered_description = filter_bmp_characters(description)
        LOGGER.info(f"Using description: {filtered_description}")

        # Upload the video
        upload_video(
            filename=video_path,
            cookies=cookies_file,
            description=filtered_description,
            headless=headless,
            privacy_type=privacy_type,
        )
        LOGGER.info(
            f"✅ Video '{video_path}' uploaded successfully (as {privacy_type})!"
        )
        return True
    except Exception as e:
        LOGGER.error(f"❌ An error occurred: {e}")
        return False
