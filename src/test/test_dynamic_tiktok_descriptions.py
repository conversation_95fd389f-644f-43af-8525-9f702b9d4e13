"""Test dynamic TikTok description generation functionality."""

import pytest
from src.bot.tiktok.tiktok import generate_dynamic_description, _detect_scenario_type


class MockStrategy:
    """Mock strategy class for testing."""
    def __init__(self, display_name, ticker_symbol, timing="regular", reinvest_dividends=False):
        self.display_name = display_name
        self.ticker_symbol = ticker_symbol
        self.investment_timing = timing
        self.reinvest_dividends = reinvest_dividends


def test_detect_scenario_dividend_comparison():
    """Test scenario detection for dividend comparison."""
    strategies = [
        MockStrategy("Apple", "AAPL", "regular", True),
        MockStrategy("Apple", "AAPL", "regular", False)
    ]
    
    result = _detect_scenario_type(strategies)
    assert result == "dividend_comparison"


def test_detect_scenario_timing_strategies():
    """Test scenario detection for timing strategies."""
    strategies = [
        MockStrategy("Apple", "AAPL", "peaks", False),
        MockStrategy("Apple", "AAPL", "lows", False)
    ]
    
    result = _detect_scenario_type(strategies)
    assert result == "timing_strategies"


def test_detect_scenario_crypto_battle():
    """Test scenario detection for crypto battle."""
    strategies = [
        MockStrategy("Bitcoin", "BTC-USD", "regular", False),
        MockStrategy("Ethereum", "ETH-USD", "regular", False)
    ]
    
    result = _detect_scenario_type(strategies)
    assert result == "crypto_battle"


def test_detect_scenario_company_showdown():
    """Test scenario detection for company showdown."""
    strategies = [
        MockStrategy("Apple", "AAPL", "regular", True),
        MockStrategy("Microsoft", "MSFT", "regular", True)
    ]
    
    result = _detect_scenario_type(strategies)
    assert result == "company_showdown"


def test_detect_scenario_single_strategy():
    """Test scenario detection for single strategy."""
    strategies = [
        MockStrategy("Apple", "AAPL", "regular", True)
    ]
    
    result = _detect_scenario_type(strategies)
    assert result == "single_strategy"


def test_generate_dynamic_description_dividend_comparison():
    """Test dynamic description generation for dividend comparison."""
    strategies = [
        MockStrategy("Apple", "AAPL", "regular", True),
        MockStrategy("Apple", "AAPL", "regular", False)
    ]
    
    result = generate_dynamic_description(
        strategies=strategies,
        investment_amount=1000,
        years_ago=10,
        interval="Month"
    )
    
    # Should contain dividend-related content
    assert any(word in result.lower() for word in ["dividend", "reinvest", "cash", "compound"])
    # Should contain engagement elements (check for common engagement words)
    assert any(word in result.lower() for word in ["would", "you", "which", "what", "tag", "follow", "like", "save"])
    # Should contain hashtags
    assert "#stocks" in result
    assert "#investing" in result


def test_generate_dynamic_description_with_music():
    """Test dynamic description generation with music attribution."""
    strategies = [
        MockStrategy("Apple", "AAPL", "regular", True),
        MockStrategy("Microsoft", "MSFT", "regular", True)
    ]
    
    result = generate_dynamic_description(
        strategies=strategies,
        investment_amount=1000,
        years_ago=10,
        interval="Month",
        music_creator="Test Artist",
        music_link="https://example.com"
    )
    
    # Should contain music attribution
    assert "Music by Test Artist" in result
    assert "https://example.com" in result
    # Should contain company showdown content
    assert any(word in result.lower() for word in ["battle", "showdown", "vs", "face-off"])


def test_generate_dynamic_description_timing_strategies():
    """Test dynamic description generation for timing strategies."""
    strategies = [
        MockStrategy("Apple", "AAPL", "peaks", False),
        MockStrategy("Apple", "AAPL", "lows", False)
    ]
    
    result = generate_dynamic_description(
        strategies=strategies,
        investment_amount=1000,
        years_ago=10,
        interval="Month"
    )
    
    # Should contain timing-related content
    assert any(word in result.lower() for word in ["peak", "low", "timing", "entry"])
    # Should contain disclaimer
    assert any(word in result for word in ["⚠️", "📊", "💡", "🎯"])


def test_generate_dynamic_description_crypto():
    """Test dynamic description generation for crypto."""
    strategies = [
        MockStrategy("Bitcoin", "BTC-USD", "regular", False),
        MockStrategy("Ethereum", "ETH-USD", "regular", False)
    ]
    
    result = generate_dynamic_description(
        strategies=strategies,
        investment_amount=1000,
        years_ago=10,
        interval="Month"
    )
    
    # Should contain crypto-related content
    assert any(word in result.lower() for word in ["crypto", "rocket", "moon", "digital"])


def test_generate_dynamic_description_different_hashtag_styles():
    """Test different hashtag styles."""
    strategies = [MockStrategy("Apple", "AAPL", "regular", True)]
    
    # Test different hashtag styles
    for style in ["traditional", "end_only", "mixed", "separated", "tiktok_style"]:
        result = generate_dynamic_description(
            strategies=strategies,
            hashtag_style=style
        )
        
        # All should contain some form of hashtags
        assert "#" in result
        # Should contain basic investment hashtags
        assert any(tag in result for tag in ["#stocks", "#investing", "#finance"])


def test_generate_dynamic_description_no_strategies():
    """Test dynamic description generation with no strategies."""
    result = generate_dynamic_description(
        strategies=None,
        investment_amount=1000,
        years_ago=10,
        interval="Month"
    )
    
    # Should default to single_strategy scenario
    assert any(word in result.lower() for word in ["investment", "journey", "strategy", "results"])
    # Should still contain engagement and hashtags
    assert "#" in result


def test_description_components_variety():
    """Test that descriptions vary due to random component selection."""
    strategies = [MockStrategy("Apple", "AAPL", "regular", True)]
    
    descriptions = []
    for _ in range(10):
        desc = generate_dynamic_description(strategies=strategies)
        descriptions.append(desc)
    
    # Should have some variety (not all identical)
    unique_descriptions = set(descriptions)
    assert len(unique_descriptions) > 1  # At least some variation due to randomization
