"""Test dynamic title generation functionality."""

import pytest
from src.utils.title_constructor import construct_dynamic_title, construct_string_title


class MockStrategy:
    """Mock strategy class for testing."""
    def __init__(self, display_name, ticker_symbol, timing="regular", reinvest_dividends=False):
        self.display_name = display_name
        self.ticker_symbol = ticker_symbol
        self.investment_timing = timing
        self.reinvest_dividends = reinvest_dividends


def test_construct_dynamic_title_dividend_comparison():
    """Test dynamic title generation for dividend comparison."""
    strategies = [
        MockStrategy("Apple", "AAPL", "regular", True),
        MockStrategy("Apple", "AAPL", "regular", False)
    ]
    
    result = construct_dynamic_title(strategies, 1000, 10, "Month")
    
    # Should contain dividend-related keywords
    assert "Apple" in result
    assert any(word in result.lower() for word in ["dividend", "reinvest", "cash", "compound"])


def test_construct_dynamic_title_timing_strategies():
    """Test dynamic title generation for timing strategies."""
    strategies = [
        MockStrategy("Apple", "AAPL", "peaks", False),
        MockStrategy("Apple", "AAPL", "lows", False)
    ]
    
    result = construct_dynamic_title(strategies, 1000, 10, "Month")
    
    # Should contain timing-related keywords
    assert "Apple" in result
    assert any(word in result.lower() for word in ["peak", "low", "timing", "high", "dip"])


def test_construct_dynamic_title_company_showdown():
    """Test dynamic title generation for multi-company comparison."""
    strategies = [
        MockStrategy("Apple", "AAPL", "regular", True),
        MockStrategy("Microsoft", "MSFT", "regular", True)
    ]
    
    result = construct_dynamic_title(strategies, 1000, 10, "Month")
    
    # Should contain both companies and battle/vs keywords
    assert "Apple" in result or "AAPL" in result
    assert "Microsoft" in result or "MSFT" in result
    assert any(word in result.lower() for word in ["vs", "battle", "showdown", "face-off"])


def test_construct_dynamic_title_crypto():
    """Test dynamic title generation for crypto assets."""
    strategies = [
        MockStrategy("Bitcoin", "BTC-USD", "regular", False),
        MockStrategy("Ethereum", "ETH-USD", "regular", False)
    ]
    
    result = construct_dynamic_title(strategies, 1000, 10, "Month")
    
    # Should contain crypto-related keywords
    assert any(word in result.lower() for word in ["crypto", "bitcoin", "ethereum", "moon", "rocket"])


def test_construct_dynamic_title_index_funds():
    """Test dynamic title generation for index funds."""
    strategies = [
        MockStrategy("S&P 500", "^GSPC", "regular", False),
        MockStrategy("Nasdaq", "^IXIC", "regular", False)
    ]
    
    result = construct_dynamic_title(strategies, 1000, 10, "Month")
    
    # Should contain index-related keywords
    assert any(word in result.lower() for word in ["index", "market", "s&p", "nasdaq"])


def test_construct_dynamic_title_single_strategy():
    """Test dynamic title generation for single strategy."""
    strategies = [
        MockStrategy("Apple", "AAPL", "regular", True)
    ]
    
    result = construct_dynamic_title(strategies, 1000, 10, "Month")
    
    # Should contain the company name and be engaging
    assert "Apple" in result
    assert len(result) > 20  # Should be a substantial title


def test_construct_dynamic_title_peaks_only():
    """Test dynamic title generation for peaks-only strategy."""
    strategies = [
        MockStrategy("Apple", "AAPL", "peaks", False)
    ]
    
    result = construct_dynamic_title(strategies, 1000, 10, "Month")
    
    # Should contain peaks-related keywords
    assert "Apple" in result
    assert any(word in result.lower() for word in ["peak", "high", "worst", "buying"])


def test_construct_dynamic_title_lows_only():
    """Test dynamic title generation for lows-only strategy."""
    strategies = [
        MockStrategy("Apple", "AAPL", "lows", False)
    ]
    
    result = construct_dynamic_title(strategies, 1000, 10, "Month")
    
    # Should contain lows-related keywords
    assert "Apple" in result
    assert any(word in result.lower() for word in ["low", "dip", "perfect", "hunter"])


def test_construct_string_title_single_company():
    """Test original title construction with single company."""
    result = construct_string_title(["Apple"], 1000, 10, "Month")
    expected = "What if you had invested $1,000 each month starting 10 years ago in Apple?"
    assert result == expected


def test_construct_string_title_two_companies():
    """Test original title construction with two companies."""
    result = construct_string_title(["Apple", "Microsoft"], 1000, 10, "Month")
    expected = "What if you had invested $1,000 each month starting 10 years ago in Apple and Microsoft?"
    assert result == expected


def test_construct_string_title_multiple_companies():
    """Test original title construction with multiple companies."""
    result = construct_string_title(["Apple", "Microsoft", "Google"], 1000, 10, "Month")
    expected = "What if you had invested $1,000 each month starting 10 years ago in Apple, Microsoft, and Google?"
    assert result == expected


def test_amount_descriptions():
    """Test that different amounts generate appropriate descriptive words."""
    strategies = [MockStrategy("Apple", "AAPL", "regular", False)]
    
    # Test different amounts
    small_result = construct_dynamic_title(strategies, 100, 10, "Month")
    medium_result = construct_dynamic_title(strategies, 1000, 10, "Month")
    large_result = construct_dynamic_title(strategies, 10000, 10, "Month")
    
    # All should contain Apple and be different (due to randomization or amount descriptions)
    assert "Apple" in small_result
    assert "Apple" in medium_result
    assert "Apple" in large_result


def test_time_descriptions():
    """Test that different time periods generate appropriate descriptive words."""
    strategies = [MockStrategy("Apple", "AAPL", "regular", False)]
    
    # Test different time periods
    short_result = construct_dynamic_title(strategies, 1000, 3, "Month")
    medium_result = construct_dynamic_title(strategies, 1000, 10, "Month")
    long_result = construct_dynamic_title(strategies, 1000, 25, "Month")
    
    # All should contain Apple
    assert "Apple" in short_result
    assert "Apple" in medium_result
    assert "Apple" in long_result
