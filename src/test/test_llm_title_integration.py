"""Test LLM title integration functionality."""

import pytest
from unittest.mock import Mock, patch
from src.utils.title_constructor import construct_llm_title, construct_string_title


class MockStrategy:
    """Mock strategy class for testing."""

    def __init__(self, display_name, timing="regular", reinvest_dividends=False):
        self.display_name = display_name
        self.timing = timing
        self.reinvest_dividends = reinvest_dividends


def test_construct_llm_title_success():
    """Test successful LLM title generation."""
    strategies = [
        MockStrategy("Apple", "regular", True),
        MockStrategy("Microsoft", "regular", False),
    ]

    with patch("src.utils.title_constructor.StonkyClient") as mock_client_class:
        mock_client = Mock()
        mock_client.generate.return_value = (
            "🚀 Apple vs Microsoft: The Ultimate Investment Showdown!"
        )
        mock_client_class.return_value = mock_client

        result = construct_llm_title(strategies, 1000, 10, "Month")

        assert result == "🚀 Apple vs Microsoft: The Ultimate Investment Showdown!"
        mock_client.generate.assert_called_once()


def test_construct_llm_title_fallback_on_empty_response():
    """Test fallback to original title when LLM returns empty response."""
    strategies = [MockStrategy("Apple")]

    with patch("src.utils.title_constructor.StonkyClient") as mock_client_class:
        mock_client = Mock()
        mock_client.generate.return_value = ""  # Empty response
        mock_client_class.return_value = mock_client

        result = construct_llm_title(strategies, 1000, 10, "Month")

        # Should fallback to original title format
        expected = (
            "What if you had invested $1,000 each month starting 10 years ago in Apple?"
        )
        assert result == expected


def test_construct_llm_title_fallback_on_exception():
    """Test fallback to original title when LLM raises exception."""
    strategies = [MockStrategy("Apple"), MockStrategy("Microsoft")]

    with patch("src.utils.title_constructor.StonkyClient") as mock_client_class:
        mock_client_class.side_effect = Exception("LLM connection failed")

        result = construct_llm_title(strategies, 1000, 10, "Month")

        # Should fallback to original title format
        expected = "What if you had invested $1,000 each month starting 10 years ago in Apple and Microsoft?"
        assert result == expected


def test_construct_string_title_single_company():
    """Test original title construction with single company."""
    result = construct_string_title(["Apple"], 1000, 10, "Month")
    expected = (
        "What if you had invested $1,000 each month starting 10 years ago in Apple?"
    )
    assert result == expected


def test_construct_string_title_two_companies():
    """Test original title construction with two companies."""
    result = construct_string_title(["Apple", "Microsoft"], 1000, 10, "Month")
    expected = "What if you had invested $1,000 each month starting 10 years ago in Apple and Microsoft?"
    assert result == expected


def test_construct_string_title_multiple_companies():
    """Test original title construction with multiple companies."""
    result = construct_string_title(["Apple", "Microsoft", "Google"], 1000, 10, "Month")
    expected = "What if you had invested $1,000 each month starting 10 years ago in Apple, Microsoft, and Google?"
    assert result == expected
