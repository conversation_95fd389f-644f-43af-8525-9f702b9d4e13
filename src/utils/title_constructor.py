import random
from typing import List


def construct_string_title(
    company_names: list[str], investment_amount: int, years_ago: int, interval: str
):
    """Constructs a descriptive title string for investment analysis.

    Creates a formatted title string that describes a hypothetical investment
    scenario, including the companies invested in, investment amount, frequency,
    and time period.

    Args:
        company_names (list[str]): List of company names to include in the title.
            If more than 2 companies, they will be formatted as "A, B, and C".
            If 2 companies, they will be formatted as "A and B".
            If 1 company, just the company name is used.
        investment_amount (int): The dollar amount invested at each interval.
        years_ago (int): The number of years ago the investment period started.
        interval (str): The investment frequency (e.g., "Month", "Week", "Year").

    Returns:
        str: A formatted title string describing the investment scenario.
            Example: "What if you had invested $1,000 each month starting 10 years ago in Apple and Microsoft?"
    """

    if len(company_names) > 2:
        ticker_str = f"{', '.join(company_names[:-1])}, and {company_names[-1]}"
    elif len(company_names) == 2:
        ticker_str = " and ".join(company_names)
    else:
        ticker_str = company_names[0]

    return f"What if you had invested ${investment_amount:,} each {interval.lower()} starting {years_ago} years ago in {ticker_str}?"


def construct_dynamic_title(
    strategies: List, investment_amount: int, years_ago: int, interval: str
) -> str:
    """Constructs an engaging, dynamic title based on investment strategies and config.

    Creates varied, engaging titles that adapt to different investment scenarios,
    timing strategies, dividend policies, and market conditions. Uses appropriate
    language for peaks, lows, showdowns, and comparisons.

    Args:
        strategies (List): List of investment strategy objects with attributes:
            - display_name: Company display name
            - investment_timing: "regular", "peaks", "lows"
            - reinvest_dividends: Boolean for dividend reinvestment
            - ticker_symbol: Stock ticker symbol
        investment_amount (int): Dollar amount invested per interval
        years_ago (int): Number of years back for analysis
        interval (str): Investment frequency ("Month", "Week", "Year", etc.)

    Returns:
        str: Dynamic, engaging title string optimized for social media
    """

    # Extract strategy information
    company_names = [s.display_name for s in strategies]
    tickers = [s.ticker_symbol for s in strategies]
    timings = [getattr(s, 'investment_timing', 'regular') for s in strategies]
    dividend_policies = [getattr(s, 'reinvest_dividends', False) for s in strategies]

    # Determine title type based on strategies
    unique_companies = len(set(tickers))
    has_timing_strategies = any(t in ['peaks', 'lows'] for t in timings)
    has_dividend_comparison = len(set(dividend_policies)) > 1
    is_crypto = any('USD' in ticker for ticker in tickers)
    is_index = any(ticker.startswith('^') for ticker in tickers)

    # Title templates organized by scenario
    if unique_companies == 1 and has_dividend_comparison:
        # Same company, different dividend strategies
        return _create_dividend_comparison_title(
            company_names[0], investment_amount, years_ago, interval
        )

    elif has_timing_strategies:
        # Timing-based strategies (peaks vs lows)
        return _create_timing_strategy_title(
            strategies, investment_amount, years_ago, interval
        )

    elif unique_companies > 1:
        # Multiple companies showdown
        return _create_company_showdown_title(
            company_names, investment_amount, years_ago, interval, is_crypto, is_index
        )

    else:
        # Single strategy or similar strategies
        return _create_single_strategy_title(
            company_names[0], investment_amount, years_ago, interval, is_crypto, is_index
        )


def _create_dividend_comparison_title(
    company_name: str, investment_amount: int, years_ago: int, interval: str
) -> str:
    """Create title for dividend reinvestment comparison."""
    templates = [
        f"💰 {company_name}: Reinvesting Dividends vs Taking Cash - Which Wins?",
        f"🔄 The {company_name} Dividend Dilemma: Reinvest or Cash Out?",
        f"💸 {company_name} Showdown: Dividend Reinvestment vs Cash Strategy",
        f"🎯 {company_name}: The Power of Dividend Reinvestment Revealed",
        f"💎 {company_name} Strategy Battle: Compound vs Cash Dividends"
    ]
    return random.choice(templates)


def _create_timing_strategy_title(
    strategies: List, investment_amount: int, years_ago: int, interval: str
) -> str:
    """Create title for timing-based strategies (peaks vs lows)."""
    company_name = strategies[0].display_name

    if any(s.investment_timing == 'peaks' for s in strategies):
        if any(s.investment_timing == 'lows' for s in strategies):
            # Peaks vs Lows comparison
            templates = [
                f"📈📉 {company_name}: Buying at Peaks vs Lows - The Ultimate Test!",
                f"🎢 {company_name} Timing Challenge: Market Highs vs Market Lows",
                f"⚡ The {company_name} Timing Experiment: Worst vs Best Entry Points",
                f"🎯 {company_name}: Peak Buyer vs Dip Buyer - Who Wins?",
                f"🔥 {company_name} Market Timing Battle: Highs vs Lows Strategy"
            ]
        else:
            # Just peaks strategy
            templates = [
                f"📈 {company_name}: What If You Only Bought at Market Peaks?",
                f"🎢 The {company_name} Peak Buyer Challenge - Worst Timing Ever?",
                f"⚡ {company_name}: Buying High Strategy - Does It Work?",
                f"🔥 {company_name} Peak Performance: Investing at Market Highs"
            ]
    else:
        # Just lows strategy
        templates = [
            f"📉 {company_name}: What If You Only Bought the Dips?",
            f"💎 The {company_name} Dip Buyer's Dream Strategy",
            f"🎯 {company_name}: Perfect Timing - Buying Every Low",
            f"⚡ {company_name} Low Hunter: The Ultimate Dip Strategy"
        ]

    return random.choice(templates)


def _create_company_showdown_title(
    company_names: List[str], investment_amount: int, years_ago: int,
    interval: str, is_crypto: bool, is_index: bool
) -> str:
    """Create title for multi-company comparisons."""

    # Format company names
    if len(company_names) > 2:
        companies_str = f"{', '.join(company_names[:-1])} vs {company_names[-1]}"
    else:
        companies_str = " vs ".join(company_names)

    # Choose appropriate templates based on asset type
    if is_crypto:
        templates = [
            f"🚀 Crypto Battle: {companies_str} - Which Moon Shot Wins?",
            f"⚡ {companies_str}: The Ultimate Crypto Showdown!",
            f"💎 Crypto Wars: {companies_str} Investment Face-Off",
            f"🔥 {companies_str}: Digital Gold Rush Comparison"
        ]
    elif is_index:
        templates = [
            f"📊 Index Fund Battle: {companies_str} - Market Showdown!",
            f"🏆 {companies_str}: The Ultimate Index Comparison",
            f"📈 Market Giants: {companies_str} Performance Battle",
            f"⚡ {companies_str}: Index Fund Championship"
        ]
    else:
        # Regular stocks
        amount_desc = _get_amount_description(investment_amount)
        time_desc = _get_time_description(years_ago)

        templates = [
            f"🥊 Stock Battle: {companies_str} - {time_desc} Showdown!",
            f"🏆 {companies_str}: The Ultimate {amount_desc} Investment Race",
            f"⚡ {companies_str}: {time_desc} Performance Battle Royale",
            f"🎯 Stock Wars: {companies_str} - Which Dominates?",
            f"🔥 {companies_str}: The {time_desc} Investment Championship",
            f"💰 {companies_str} Face-Off: {amount_desc} {interval.lower()}ly for {years_ago} years",
            f"📈 The Great {companies_str} Showdown: {time_desc} Results!"
        ]

    return random.choice(templates)


def _create_single_strategy_title(
    company_name: str, investment_amount: int, years_ago: int,
    interval: str, is_crypto: bool, is_index: bool
) -> str:
    """Create title for single company/strategy."""

    amount_desc = _get_amount_description(investment_amount)
    time_desc = _get_time_description(years_ago)

    if is_crypto:
        templates = [
            f"🚀 {company_name}: The {amount_desc} Crypto Journey - {time_desc} Results!",
            f"⚡ {company_name} Moon Mission: {amount_desc} {interval.lower()}ly for {years_ago} years",
            f"💎 {company_name}: Diamond Hands {time_desc} Strategy",
            f"🔥 {company_name} Rocket Fuel: {amount_desc} DCA Strategy"
        ]
    elif is_index:
        templates = [
            f"📊 {company_name}: The {amount_desc} Index Strategy - {time_desc} Results",
            f"🏆 {company_name}: Steady {amount_desc} {interval.lower()}ly Growth Story",
            f"📈 {company_name}: The {time_desc} Market Ride",
            f"⚡ {company_name}: {amount_desc} Index Fund Journey"
        ]
    else:
        templates = [
            f"🎯 {company_name}: The {amount_desc} {time_desc} Challenge",
            f"💰 {company_name}: {amount_desc} {interval.lower()}ly for {years_ago} years - The Results!",
            f"🚀 {company_name}: The {time_desc} Investment Journey",
            f"⚡ {company_name}: {amount_desc} DCA Strategy Revealed",
            f"🔥 {company_name}: The {time_desc} Wealth Building Story",
            f"📈 {company_name}: {amount_desc} {interval.lower()}ly - Was It Worth It?"
        ]

    return random.choice(templates)


def _get_amount_description(amount: int) -> str:
    """Get descriptive text for investment amount."""
    if amount >= 10000:
        return "Big Money"
    elif amount >= 5000:
        return "Serious"
    elif amount >= 1000:
        return "Smart"
    elif amount >= 500:
        return "Steady"
    else:
        return "Small"


def _get_time_description(years: int) -> str:
    """Get descriptive text for time period."""
    if years >= 20:
        return "Epic"
    elif years >= 15:
        return "Long-Term"
    elif years >= 10:
        return "Decade"
    elif years >= 5:
        return "Multi-Year"
    else:
        return "Short-Term"
